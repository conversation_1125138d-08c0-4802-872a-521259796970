# IronBeam Trading System - 9+ Provider Ecosystem Configuration
# Copy this file to your project root as .env

# =============================================================================
# TRADITIONAL FINANCIAL DATA PROVIDERS
# =============================================================================

# IronBeam API Configuration (Futures Trading Platform)
# Get from: https://www.ironbeam.com/
# Process: Open trading account, request API access
IRONBEAM_USERNAME=********
IRONBEAM_PASSWORD=730511
IRONBEAM_API_KEY=d6e76fb8d9b84110adb883c22386f6d2
IRONBEAM_ENVIRONMENT=sandbox  # or 'production'

# Federal Reserve Economic Data (FRED) API Key
# Get from: https://fred.stlouisfed.org/docs/api/api_key.html
# Process: Create free account, generate API key in account settings
# Limits: 120,000 requests/day (very generous)
FRED_API_KEY=a0c0a56a156c2003cb23eb7fa3bbb804

# Energy Information Administration (EIA) API Key
# Get from: https://www.eia.gov/opendata/register.php
# Process: Register with email, receive key via email
# Limits: 5,000 requests/hour (generous)
EIA_API_KEY=OiHU6aDJVV1WEfDn2ttxwPkxuFJjkSFeQQ2ZeD0F

# NOAA Climate Data Online (CDO) Token
# Get from: https://www.ncdc.noaa.gov/cdo-web/token
# Process: Enter email, receive token via email
# Limits: 1,000 requests/day (sufficient for weather data)
NOAA_CDO_TOKEN=VMQxFsuTbdlbFjovkqjvWAIUfatUpvjm
NOAA_API_KEY=VMQxFsuTbdlbFjovkqjvWAIUfatUpvjm

# USDA NASS (National Agricultural Statistics Service) API Key
# Get from: https://quickstats.nass.usda.gov/api
# Process: Enter email, receive key instantly
# Limits: 50,000 requests/day (very generous)
USDA_NASS_API_KEY=EE79AF68-E47F-3C74-A62F-361D5F53C21D
NASS_API_KEY=EE79AF68-E47F-3C74-A62F-361D5F53C21D

# CFTC (Commodity Futures Trading Commission) - No API Key Required
# Public data source: https://publicreporting.cftc.gov/
# Process: No registration required, completely free
# Limits: No official limits (reasonable use expected)






# =============================================================================
# CRYPTOCURRENCY DATA PROVIDERS (ALL FREE)
# =============================================================================

# CoinGecko API - No API Key Required for Basic Tier
# Get from: https://www.coingecko.com/en/api/pricing (for higher limits)
# Process: No registration required for basic use
# Basic Limits: 10-50 calls/minute (varies by endpoint)
# Pro Limits: 500 calls/minute ($129/month) - OPTIONAL for higher volume
COINGECKO_API_KEY=  # Optional - leave empty for free tier

# CoinPaprika API - No API Key Required for Basic Tier
# Get from: https://coinpaprika.com/api/ (for higher limits)
# Process: No registration required for basic use
# Basic Limits: 25,000 calls/month (very generous)
# Pro Limits: 100,000+ calls/month ($29+/month) - OPTIONAL
COINPAPRIKA_API_KEY=  # Optional - leave empty for free tier

# Crypto Fear & Greed Index - No API Key Required
# Source: https://api.alternative.me/fng/
# Process: No registration required, completely free
# Limits: No official limits (reasonable use expected)
CRYPTO_FEAR_GREED_API_KEY=  # Not required - completely free

# =============================================================================
# ADDITIONAL FREE CRYPTO PROVIDERS (PHASE 2 EXPANSION)
# =============================================================================

# Binance Public API - No API Key Required for Market Data
# Get from: https://binance-docs.github.io/apidocs/spot/en/
# Process: No registration required for public market data
# Limits: 1200 requests/minute (very generous)
BINANCE_PUBLIC_API_KEY=  # Not required for public data

# Coinbase Pro Public API - No API Key Required for Market Data
# Get from: https://docs.cloud.coinbase.com/exchange/docs
# Process: No registration required for public market data
# Limits: 10 requests/second (generous)
COINBASE_PUBLIC_API_KEY=  # Not required for public data

# Kraken Public API - No API Key Required for Market Data
# Get from: https://docs.kraken.com/rest/
# Process: No registration required for public market data
# Limits: 1 request/second (sufficient for our needs)
KRAKEN_PUBLIC_API_KEY=  # Not required for public data

# Messari API - Free Tier Available
# Get from: https://messari.io/api/docs
# Process: Register for free account to get higher limits
# Basic Limits: 20 requests/minute (no registration)
# Free Account: 200 requests/minute (with registration)
# Register at: https://messari.io/account/api
MESSARI_API_KEY=  # Optional - register for higher limits

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment Settings
ENVIRONMENT=development  # development, staging, production
LOG_LEVEL=INFO
DEBUG=true

# =============================================================================
# PROVIDER SUMMARY (9+ PROVIDERS)
# =============================================================================
#
# TRADITIONAL PROVIDERS (6):
# 1. IronBeam - Real crypto futures trading (PAID - trading account required)
# 2. FRED - Economic data (FREE - 120k requests/day)
# 3. EIA - Energy data (FREE - 5k requests/hour)
# 4. NOAA - Weather data (FREE - 1k requests/day)
# 5. USDA - Agricultural data (FREE - 50k requests/day)
# 6. CFTC - Positioning data (FREE - unlimited)
#
# CRYPTOCURRENCY PROVIDERS (3+ FREE):
# 7. CoinGecko - Crypto spot prices (FREE - 10-50 calls/min)
# 8. CoinPaprika - Crypto market data (FREE - 25k calls/month)
# 9. Crypto Sentiment - Fear & Greed Index (FREE - unlimited)
#
# ADDITIONAL CRYPTO PROVIDERS (4+ FREE - PHASE 2):
# 10. Binance Public - Real-time crypto data (FREE - 1200 calls/min)
# 11. Coinbase Pro - Real-time crypto data (FREE - 10 calls/sec)
# 12. Kraken Public - Real-time crypto data (FREE - 1 call/sec)
# 13. Messari - On-chain metrics (FREE - 200 calls/min with account)
#
# TOTAL: 13+ PROVIDERS
# COST: 92%+ FREE (only IronBeam requires paid account)
# =============================================================================


